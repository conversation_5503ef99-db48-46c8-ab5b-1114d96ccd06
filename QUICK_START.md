# 🚀 Quick Start Guide - APK Automation Testing

This guide will get you up and running with APK automation testing in just a few minutes!

## ⚡ Prerequisites Check

Before starting, make sure you have:
- ✅ **Node.js** (v14 or higher)
- ✅ **Java** (JDK 8 or higher)
- ✅ **Android SDK** with ADB
- ✅ **Android device or emulator** running

## 🏃‍♂️ Quick Setup (5 minutes)

### 1. Run Setup Script
```bash
node setup.js
```
This will check your environment and install necessary dependencies.

### 2. Install Project Dependencies
```bash
npm run install:deps
```

### 3. Start Android Device
```bash
# Check connected devices
adb devices

# Should show your device/emulator
```

### 4. Inspect Your App (Optional but Recommended)
```bash
npm run inspect
```
This will analyze your APK and provide element selectors for testing.

### 5. Run Tests
```bash
# Run all tests
npm test

# Or run specific test suites
npm run test:basic      # Basic functionality tests
npm run test:advanced   # Advanced testing scenarios
```

## 📱 Device Setup

### For Android Emulator:
1. Open Android Studio
2. Start AVD Manager
3. Create/Start an emulator
4. Verify: `adb devices`

### For Real Device:
1. Enable Developer Options
2. Enable USB Debugging
3. Connect via USB
4. Accept debugging prompt
5. Verify: `adb devices`

## 🔧 Configuration

### Update APK Path (if needed)
Edit `wdio.conf.js`:
```javascript
'appium:app': path.join(process.cwd(), 'your-app-name.apk'),
```

### Update Device Settings
Edit `wdio.conf.js`:
```javascript
'appium:deviceName': 'Your Device Name',
'appium:platformVersion': '11.0', // Your Android version
```

## 🧪 Running Tests

### Start Appium Server (Manual)
```bash
npm run appium:start
```
Then in another terminal:
```bash
npm test
```

### All-in-One (Automatic)
```bash
npm test
```
This automatically starts Appium and runs tests.

## 📊 Test Results

After running tests, check:
- **Screenshots**: `./screenshots/` - Visual verification
- **Reports**: `./test-results/` - Detailed analysis
- **Console**: Real-time test progress

## 🐛 Quick Troubleshooting

### App doesn't launch?
```bash
# Check APK exists
ls -la *.apk

# Check device connection
adb devices

# Restart ADB
adb kill-server && adb start-server
```

### Elements not found?
```bash
# Inspect your app first
npm run inspect

# Update selectors in test/pageobjects/AppPage.js
```

### Appium connection issues?
```bash
# Check if Appium is running
curl http://localhost:4723/wd/hub/status

# Restart Appium
pkill -f appium
npm run appium:start
```

## 📚 Next Steps

1. **Customize Tests**: Update `test/pageobjects/AppPage.js` with your app's elements
2. **Add Test Cases**: Create new test files in `test/specs/`
3. **Review Results**: Check screenshots and reports
4. **Iterate**: Refine tests based on your app's functionality

## 🎯 Test Examples

### Basic Test Structure
```javascript
it('should test login functionality', async () => {
    await AppPage.setText(AppPage.usernameField, 'testuser');
    await AppPage.setText(AppPage.passwordField, 'password123');
    await AppPage.clickElement(AppPage.loginButton);
    
    // Verify login success
    expect(await AppPage.isDisplayed(AppPage.welcomeText)).toBe(true);
});
```

### Element Selectors
```javascript
// XPath
get loginButton() {
    return $('//android.widget.Button[@text="Login"]');
}

// UiSelector
get loginButton() {
    return $('android=new UiSelector().textContains("Login")');
}

// Accessibility ID
get loginButton() {
    return $('~login-button');
}
```

## 🆘 Need Help?

1. Check the full `README.md` for detailed documentation
2. Review test examples in `test/specs/`
3. Use `npm run inspect` to understand your app structure
4. Check console output for error messages

---

**Happy Testing! 🎉**

*Total setup time: ~5 minutes*
